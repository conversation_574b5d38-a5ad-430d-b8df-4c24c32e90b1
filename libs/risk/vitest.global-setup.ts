

import { MySqlContainer, StartedMySqlContainer } from '@testcontainers/mysql';

import type { TestProject } from 'vitest/node';

import { dbConnect } from './src/modules/database/database-connect.js';
import { Events } from './src/modules/test/topics.js';

// This package should be consistent with the
// MySQL used in all environments
const DEFAULT_MYSQL_CONTAINER = 'mysql:8.0.32';

let mysql: StartedMySqlContainer;

export default async function setup({ provide }: TestProject) {
  // Note: We'll skip mounting migrations during container startup
  // and run them manually after the container is ready
  const mounts: any[] = [];

  // Set Docker host for macOS Docker Desktop compatibility
  process.env.DOCKER_HOST = process.env.DOCKER_HOST || 'unix:///var/run/docker.sock';
  process.env.TESTCONTAINERS_HOST_OVERRIDE = process.env.TESTCONTAINERS_HOST_OVERRIDE || 'localhost';

  try {
    mysql = await new MySqlContainer(DEFAULT_MYSQL_CONTAINER)
      .withBindMounts(mounts)
      .withExposedPorts(3306)
      .withEnvironment({
        MYSQL_ROOT_PASSWORD: 'test',
        MYSQL_DATABASE: 'testdb'
      })
      .withStartupTimeout(120_000)
      .withCommand(['--default-authentication-plugin=mysql_native_password'])
      .start();
  } catch (error) {
    console.log('MySqlContainer failed, trying GenericContainer approach:', error instanceof Error ? error.message : String(error));

    // Fallback: Use docker run directly with fixed port
    console.log('Attempting direct docker run with fixed port...');

    // Use a fixed port for local development
    const fixedPort = 33061;

    // Create a mock container interface that uses the fixed port
    mysql = {
      getHost: () => 'localhost',
      getPort: () => fixedPort,
      getMappedPort: (_port: number) => fixedPort,
      getDatabase: () => 'testdb',
      getUsername: () => 'root',
      getRootPassword: () => 'test',
      stop: async () => {
        // Stop the container by name
        try {
          const { execSync } = require('child_process');
          execSync('docker stop vitest-mysql-risk || true', { stdio: 'ignore' });
          execSync('docker rm vitest-mysql-risk || true', { stdio: 'ignore' });
        } catch (e) {
          // Ignore errors during cleanup
        }
      }
    } as unknown as StartedMySqlContainer;

    // Start MySQL container with docker run directly
    const { execSync } = require('child_process');

    // Clean up any existing container
    try {
      execSync('docker stop vitest-mysql-risk || true', { stdio: 'ignore' });
      execSync('docker rm vitest-mysql-risk || true', { stdio: 'ignore' });
    } catch (e) {
      // Ignore cleanup errors
    }

    // Start new container
    const dockerCmd = [
      'docker run -d',
      '--name vitest-mysql-risk',
      `-p ${fixedPort}:3306`,
      '-e MYSQL_ROOT_PASSWORD=test',
      '-e MYSQL_DATABASE=testdb',
      DEFAULT_MYSQL_CONTAINER,
      '--default-authentication-plugin=mysql_native_password'
    ].join(' ');

    console.log('Starting MySQL container:', dockerCmd);
    execSync(dockerCmd);

    // Wait for MySQL to be ready
    console.log('Waiting for MySQL to be ready...');
    let retries = 30;
    while (retries > 0) {
      try {
        execSync(`docker exec vitest-mysql-risk mysqladmin ping -h localhost -u root -ptest`, { stdio: 'ignore' });
        console.log('MySQL is ready!');
        break;
      } catch (e) {
        retries--;
        if (retries === 0) {
          throw new Error('MySQL failed to start within timeout');
        }
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    // Give MySQL a bit more time to fully initialize
    console.log('Waiting additional time for MySQL to fully initialize...');
    await new Promise(resolve => setTimeout(resolve, 3000));
  }

  try {
    console.log('Connecting to database...');
    const db = dbConnect({
      databaseHost: mysql.getHost(),
      databaseName: mysql.getDatabase(),
      databasePassword: mysql.getRootPassword(),
      databasePort: mysql.getPort(),
      databaseUser: mysql.getUsername(),
    });

    console.log('Setting up database schema...');

    // Create the outbox_topics table if it doesn't exist
    await db.schema
      .createTable('outbox_topics')
      .ifNotExists()
      .addColumn('id', 'varchar(255)', (col) => col.primaryKey())
      .addColumn('name', 'varchar(255)', (col) => col.notNull())
      .addColumn('created_at', 'timestamp', (col) => col.defaultTo('CURRENT_TIMESTAMP'))
      .execute();

    console.log('Inserting test data...');
    // Insert decisioning events
    await db.insertInto('outbox_topics').values(Events).onDuplicateKeyUpdate({}).execute();
    console.log('Database setup completed successfully!');
  } catch (error) {
    console.error('Database setup failed:', error instanceof Error ? error.message : String(error));
    throw error;
  }

  provide('MYSQL_HOST', mysql.getHost());
  provide('MYSQL_PORT', mysql.getMappedPort(3306).toString());
  provide('MYSQL_USER', mysql.getUsername());
  provide('MYSQL_PASSWORD', mysql.getRootPassword());
  provide('MYSQL_DATABASE', mysql.getDatabase());

  return async function teardown() {
    if (mysql) {
      await mysql.stop();
    }
  };
}

declare module 'vitest' {
  export interface ProvidedContext {
    MYSQL_HOST: string;
    MYSQL_PORT: string;
    MYSQL_USER: string;
    MYSQL_PASSWORD: string;
    MYSQL_DATABASE: string;
  }
}

// mark this file as a module so augmentation works correctly
export {};
